
  <style>
     h1,h2{margin:8px 0}
    .tabs{display:flex;gap:8px;margin-bottom:12px}
    .tab-btn{padding:8px 14px;border-radius:6px;border:1px solid #ddd;background:#fff;color:#ddd;cursor:pointer;font-size:28px;}
    .tab-btn.active{background:#222;color:#fff}
    .top-text{padding:10px;border-radius:6px;margin-bottom:12px}
    .group{border:1px solid #eee;border-radius:6px;margin-bottom:10px}
    .group > summary{padding:10px;cursor:pointer;font-weight:600;background-color:#F4C542;}
    .questions{padding:10px}
    .q{margin-bottom:10px;border:1px solid #eee;background-color:#fff;padding:5px;}
    .q label{display:block;margin-bottom:6px}
    .answers label{margin-right:12px}
    .controls{display:flex;gap:8px;align-items:center;margin-top:12px}
    button.calc{padding:8px 12px;border-radius:6px;border:none;cursor:pointer}
    button.next{}
    .result{margin-top:12px;padding:12px;border-radius:6px;border:1px dashed #ccc;background:#fff}
    table{width:100%;border-collapse:collapse;margin-top:8px}
    td,th{padding:6px;border-bottom:1px solid #eee;text-align:left}
    .hidden{display:none}
    .summary{margin-top:14px;padding:10px;background:#fff;border-radius:6px}
    .small{font-size:13px;color:#666}
    footer{margin-top:18px;font-size:13px;color:#555}
  </style>
  <div class="container">
    <div class="tabs" id="tabs"></div>

    <div id="panel"></div>

    <div id="finalSummary" class="hidden"></div>

    <footer class="small"></footer>
  </div>

<script>
// Data: three tabs (Thamo, Rajo, Sattva). Each has topHtml, bottomHtml, groups (A-E) with 5 questions each and optional group solution.
const TABS = [
  {
    id:'thamo', title:'தமோ',
    topHtml:`<div class="small1">
<p>
    <strong>🌺 தமோ குணத்தை தெளிவாகப் புரிந்துகொள்வோம் 🌺</strong>
</p>
<p>
    தமோ குணம் என்பது அறியாமை, சோம்பல், பயம், குழப்பம், தாமதம், மற்றும் தப்பித்தல் போன்ற மனநிலைகளை உருவாக்கும் ஒரு உள் சக்தி.<br>
    தேவி பாகவதம் கூறுவது போல, துர்க்கை சக்தி தான் இந்த தமோ குணத்தை மாற்றும் தெய்வீக சக்தி.<br>
    தமஸ் நம் வாழ்க்கையில் அதிகமாக இருந்தால், நாம் விழிப்புணர்வின்றி வாழ்வோம்;<br>
    வாழ்க்கையில் முன்னேற்றம் இல்லை, ஆன்மிக வளர்ச்சி தடைப்படும், உடல் மற்றும் மன ஆற்றல் குறைந்து விடும்.
</p>
<p>
    இதை உணர்வதற்காக இந்த சுய பரீட்சை வடிவமைக்கப்பட்டுள்ளது.<br>
    தமஸ் எந்த பகுதிகளில் அதிகம் செயல்படுகிறது என்பதை அறிய, கேள்விகள் ஐந்து பிரிவாகப் பிரிக்கப்பட்டுள்ளன.<br>
    இப்போது ஒவ்வொரு பகுதியும் எதை குறிக்கிறது, அதில் உள்ள கேள்விகளின் பயன் என்ன என்பதை பார்ப்போம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>A. உடல் மற்றும் ஆற்றல் நிலை (Physical Energy)</strong>
</p>
<p>
    தமஸ் உடலில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    அதிக சோம்பல்
</p>
<p>
    தூக்கம் அதிகமாகும்
</p>
<p>
    உடல் எப்போதும் சோர்வாக உணர்வு
</p>
<p>
    வேலை செய்யும் ஆர்வம் இல்லாமை
</p>
<p>
    இந்த பகுதியில் உள்ள கேள்விகள் மூலம்,<br>
    நீங்கள் தினசரி எழுச்சியுடனும் புத்துணர்ச்சியுடனும் செயல்படுகிறீர்களா,<br>
    அல்லது சோம்பல் மற்றும் உடல் மந்தநிலை உங்களை கட்டுப்படுத்துகிறதா என்பதை தெளிவாக அறியலாம்.
</p>
<p>
    <strong>பயன்:</strong><br>
    உடல் தமஸ் அதிகமெனில், தினசரி உடற்பயிற்சி, சரியான உணவு பழக்கம், மூச்சுப் பயிற்சி போன்றவற்றைச் செய்ய வேண்டிய அவசியத்தை உணரலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>B. மனநிலை மற்றும் உணர்ச்சி (Mental State)</strong>
</p>
<p>
    தமஸ் மனதில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    காரணமில்லா பயம்
</p>
<p>
    எதிர்மறை சிந்தனை
</p>
<p>
    புதிய முயற்சிகளைத் தொடங்க முடியாத பயம்
</p>
<p>
    விமர்சனத்தால் எளிதில் மனம் தளருதல்
</p>
<p>
    இந்த கேள்விகள், உங்கள் மனதில் எவ்வளவு தெளிவு உள்ளது,<br>
    எதிர்மறை எண்ணங்கள் மற்றும் பயங்கள் எவ்வளவு மேலோங்குகின்றன என்பதை வெளிப்படுத்தும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    மன தமஸ் அதிகமாக இருந்தால், தியானம், மூச்சுப் பயிற்சி, துர்க்கை மந்திர ஜபம் போன்றவை மனதை சுத்தப்படுத்த உதவும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>C. செயல்முறை (Action)</strong>
</p>
<p>
    தமஸ் செயலில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    வேலைகளை தள்ளிப்போடுதல்
</p>
<p>
    நடுவில் வேலை நிறுத்திவிடுதல்
</p>
<p>
    சிக்கல் வந்தவுடன் முயற்சியை கைவிடுதல்
</p>
<p>
    நேர மேலாண்மை இல்லாமை
</p>
<p>
    இந்த கேள்விகள் மூலம்,<br>
    நீங்கள் செய்ய வேண்டியவற்றை தொடர்ந்து நிறைவேற்றுகிறீர்களா அல்லது சோம்பல் மற்றும் தாமதம் காரணமாக பின்தங்குகிறீர்களா என்பதை அறியலாம்.
</p>
<p>
    <strong>பயன்:</strong><br>
    செயல் தமஸ் அதிகமெனில், சிறிய இலக்குகளை அமைத்து அதனைச் சிறு முயற்சிகளால் நிறைவேற்றும் பழக்கத்தை வளர்க்க வேண்டும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>D. உறவுகள் மற்றும் சமூக பழக்கம் (Relationships)</strong>
</p>
<p>
    தமஸ் உறவுகளில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    பொறுப்புகளை பிறரிடம் தள்ளிவிடுதல்
</p>
<p>
    பிறர் ஊக்கமின்றி செயல்பட முடியாமை
</p>
<p>
    எப்போதும் பிறரை குறை கூறுதல்
</p>
<p>
    குழுவில் வேலை செய்யாமை, தனிமைப்படுதல்
</p>
<p>
    சமூக நிகழ்வுகளைத் தவிர்த்தல்
</p>
<p>
    இந்த கேள்விகள்,<br>
    உங்கள் சமூக பங்குபற்றுதல் மற்றும் பொறுப்புணர்வு எவ்வளவு தமஸால் பாதிக்கப்பட்டுள்ளது என்பதை காட்டும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    உறவு தமஸ் அதிகமெனில், குழுவில் சிறிய பங்குகளை ஏற்று செயல்படத் தொடங்குவது முக்கியம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>E. ஆன்மிக முன்னேற்றம் (Spiritual Growth)</strong>
</p>
<p>
    தமஸ் ஆன்மிகத்தில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    ஜபம், தியானம் தொடர்ந்து செய்ய முடியாமை
</p>
<p>
    ஆன்மிக நூல்கள் படிக்க தள்ளிப்போடுதல்
</p>
<p>
    ஜபத்தின் போது தூக்கம், கவனச்சிதறல்
</p>
<p>
    பொறாமை, கோபம், ஆசை போன்ற குற்றங்கள் குறையாதது
</p>
<p>
    ஆன்மிக வளர்ச்சி தூரமானது என்று நினைப்பது
</p>
<p>
    இந்த கேள்விகள்,<br>
    உங்கள் ஆன்மிக பாதையில் உள்ள தடைகள் தமஸால் எவ்வளவு ஏற்படுகின்றன என்பதை வெளிப்படுத்தும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    ஆன்மிக தமஸ் அதிகமெனில், தினசரி குறைந்த நேரத்திலாவது தியானம், ஜபம் செய்வதை தொடங்க வேண்டும்.<br>
    துர்க்கை மந்திர ஜபம் தமஸை குறைக்கும் முக்கிய வழியாகும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>தமஸ் எவ்வாறு எம்மில் செயற்படுகிறது</strong>
</p>
<p>
    தமஸ் என்பது இருள் போன்றது.<br>
    விழிப்புணர்வின்றி வாழும் நிலையை உருவாக்கி,
</p>
<p>
    மனம் சோம்பலாகி செயலற்றதாகும்,
</p>
<p>
    உடல் ஆர்வமற்றதாகி சோர்வடையும்,
</p>
<p>
    உறவுகள் பாதிக்கப்படும்,
</p>
<p>
    ஆன்மிக முன்னேற்றம் தடைப்படும்.
</p>
<p>
    தமஸ் அதிகமாக இருந்தால், வாழ்க்கை சுழல் வட்டத்தில் சிக்கி முன்னேற்றமின்றி தொடரும்.<br>
    அதனால் தமஸை அடையாளம் காண்பது மிக அவசியம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>ஏன் இந்த பரீட்சை செய்ய வேண்டும்?</strong>
</p>
<p>
    இந்த பரீட்சை உங்களுக்கு:
</p>
<p>
    தமஸ் எந்த பகுதியில் அதிகமாக செயல்படுகிறது என்பதை தெளிவாகக் காட்டும்.
</p>
<p>
    எந்த பழக்கங்களை மாற்ற வேண்டும் என்பதை சரியாகக் காண உதவும்.
</p>
<p>
    துர்க்கை சாதனையை எந்த முறையில் தொடங்க வேண்டும் என்பதற்கு தெளிவு தரும்.
</p>
<p>
    “தமஸ் என்பது இருள்;<br>
    விழிப்புணர்வான ஆராய்ச்சி தான் அதன் மீது ஒளி பாயும் விளக்கு.” 🌺
</p>
<p>
    இந்த சுய பரீட்சையைச் செய்து,<br>
    தமஸை அடையாளம் கண்டு,<br>
    துர்க்கை சக்தியின் அருளால் உங்கள் வாழ்க்கையை வெளிச்சமாக மாற்றுங்கள். 🌟
</p>
<h2>🌺 தமோ குணத்தின் தாக்கத்தை அறிய சுய பரீட்சை 🌺</h2></div>`,
    bottomHtml:`<div class="small"><b>ஆராயும் முறை</b>

அதிக மதிப்பெண் பெற்ற பகுதி எது என பார்க்கவும்:<br>

உடல், மனம், செயல், உறவுகள், ஆன்மிகம் – இதில் எது அதிக மதிப்பெண் பெற்றது?<br>

அதுவே உங்கள் தமஸ் குணம் மிகுந்து செயல்படும் பகுதி.<br>

குறிப்பிட்ட கேள்விகளைத் தேர்வு செய்யவும்:<br>

அதிக மதிப்பெண் பெற்ற பகுதிக்குள், "ஆம்" என்று பதிலளித்த கேள்விகளைத் தேர்வு செய்யுங்கள்.<br>

அவை உங்கள் வாழ்க்கையில் மாற்றம் செய்ய வேண்டிய முக்கிய இடங்கள்.<br>

நடவடிக்கை திட்டம்:<br>

தினசரி 10 நிமிடம் அந்த பழக்கத்தை மாற்ற சிறிய இலக்குகளை அமைக்கவும்.<br>

உதாரணம்: "நாளை காலை 10 நிமிடம் ஜபம் செய்வேன்" அல்லது
"ஒரு வேலை இன்று தொடங்கி நிறுத்தாமல் முடிப்பேன்."<br>

மந்திரம் மற்றும் தியானம்:
<br>
oṁ duṁ durgāyai namaḥ மந்திரத்தை 108 முறை ஜபியுங்கள்.<br>

ஜபத்துக்கு முன், தமஸ் தாக்கத்தை குறைக்கும் நோக்கத்துடன் நிம்மதியான மூச்சுப் பயிற்சி செய்யுங்கள்.
<br>
<b>வாராந்திர சுய மதிப்பாய்வு</b><br>

7 நாட்கள் கழித்து மீண்டும் சுய பரீட்சையைச் செய்யுங்கள்.<br>

உங்கள் மதிப்பெண் குறைந்தால், துர்க்கை சக்தி விழிப்புணர்வை அளிக்கத் தொடங்கியிருக்கிறது என்பதற்கான அறிகுறி.
<br>
இதை தொடர்ந்து 4 வாரங்கள் செய்யுங்கள்.
<br>
<b>முடிவு</b><br>

தமோ குணத்தின் தாக்கத்தை அறிதல் என்பது ஆன்மிக சாதனையின் முதல் படி.<br>
இந்த சுய பரீட்சை உங்களை நேர்மையாக ஆராய உதவும்.<br>
நீங்கள் எங்கு நிற்கிறீர்கள் என்பதை அறிந்த பிறகு,
துர்க்கை சக்தியின் சாதனையை மேற்கொண்டு,
உங்கள் உள்ளார்ந்த இருளை வெளிச்சமாக மாற்றிக் கொள்ள முடியும்.
<br>
தமஸ் என்பது இருள்;
விழிப்புணர்வான ஆராய்ச்சி தான் அதன் மீது ஒளி பாயும் விளக்கு. 🌺</div>`,
    ranges:[{min:0,max:15,txt:'தமஸ் தாக்கம் மிகக் குறைவு. மனம் விழிப்புணர்வுடன் உள்ளது.'},{min:16,max:30,txt:'தமஸ் தாக்கம் நடுத்தர அளவில் உள்ளது. மேம்பாடு தேவை.'},{min:31,max:40,txt:'தமஸ் தாக்கம் அதிகம். துர்க்கை சக்தி சாதனை அவசியம்.'},{min:41,max:50,txt:'தமஸ் மிக ஆழமாக உள்ளது. உடனடி ஆன்மிக மற்றும் வாழ்க்கை மாற்ற முயற்சி தேவை.'}],
    groups:[
      {key:'A',title:'உடல் மற்றும் ஆற்றல் நிலை (Physical Energy)',solution:'' ,q:[
        'காலை எழுந்தவுடன் புத்துணர்ச்சி இல்லாமல் சோர்வாக உணர்கிறேனா?',
        'ஒரு வேலை செய்யத் தொடங்கும்போது உடனே சோர்வு அல்லது தூக்கத்தால் தள்ளிப்போகிறேனா?',
        'என் உடல் இயக்கங்கள் மிகவும் மந்தமாக உள்ளனவா? (மெதுவாக நடந்தல், சோர்வான முகம்)',
        'உடற்பயிற்சி செய்யும் ஆர்வம் இல்லாமல் எப்போதும் தள்ளிப்போடுகிறேனா?',
        'நோய், உடல் வலி போன்ற காரணங்களை அடிக்கடி சோம்பலுக்கான காரணமாக பயன்படுத்துகிறேனா?'
      ]},
      {key:'B',title:'மனநிலை மற்றும் உணர்ச்சி (Mental State)',solution:'',q:[
        'எனக்கு அடிக்கடி காரணமில்லா பயம் அல்லது குழப்பம் ஏற்படுகிறதா?',
        '"நான் முடியாது" அல்லது "இது எனக்கு கடினம்" என்று முன்பே முடிவு செய்து விடுகிறேனா?',
        'வாழ்க்கையில் புதிய முயற்சிகளை செய்ய பயப்படுகிறேனா?',
        'எதிர்மறை நினைப்புகள் (Negative thoughts) அதிக நேரம் எனது மனதில் நிலைத்திருக்கிறதா?',
        'பிறர் என்னை விமர்சித்தால் உடனே மனம் தளர்ந்து, நீண்ட நேரம் அதை மறக்க முடியாமல் இருக்கிறேனா?'
      ]},
      {key:'C',title:'செயல்முறை (Action)',solution:'',q:[
        'எனக்கு ஒரு வேலை தொடங்க வேண்டும் என்று இருந்தாலும் தொடர்ந்து தள்ளிப்போடுகிறேனா?',
        'ஒரு செயலை தொடங்கிய பின் நடுவில் நிறுத்திவிடும் பழக்கம் இருக்கிறதா?',
        'சிறிய சிக்கல் வந்தால் உடனே முயற்சியை கைவிடுகிறேனா?',
        'என்ன செய்ய வேண்டும் என்று தெரிந்திருந்தும், "பின்னால் செய்வேன்" என்று சொல்லி விடுகிறேனா?',
        'நேரம் கட்டுப்பாட்டுடன் வேலை செய்வது கடினமாக உள்ளதா?'
      ]},
      {key:'D',title:'உறவுகள் மற்றும் சமூக பழக்கம் (Relationships)',solution:'',q:[
        'பிறரிடம் பொறுப்புகளை அடிக்கடி தள்ளிவிட்டு, "நீங்களே செய்து விடுங்கள்" என்று சொல்கிறேனா?',
        'பிறர் என்னை ஊக்கப்படுத்தாமல் இருந்தால், நான் சுயமாக எந்த செயலும் செய்ய முடியாமல் போகிறேனா?',
        'நண்பர்கள்/குடும்பத்தில் ஒருவரை குறை கூறுவது என் பழக்கமாக இருக்கிறதா?',
        'குழுவில் வேலை செய்யும்போது, "எனக்கு வேலை வேண்டாம்" என்று விலகி நிற்கிறேனா?',
        'சமூக நிகழ்வுகளைத் தவிர்க்கும் பழக்கம் அதிகமா?'
      ]},
      {key:'E',title:'ஆன்மிக முன்னேற்றம் (Spiritual Growth)',solution:'',q:[
        'ஜபம் அல்லது தியானம் தொடங்க முயற்சி செய்தும், சோம்பலால் தொடர்ந்து செய்ய முடியவில்லையா?',
        'ஆன்மிக நூல்கள் படிக்க வேண்டும் என்று நினைத்தும், அதை தள்ளிப்போடுகிறேனா?',
        'மந்திரம் ஜபிக்கும் போது கவனம் சிதறி தூக்கம் வருகிறதா?',
        'சாத்தான பக்கங்கள் (பொறாமை, கோபம், ஆசை) குறையாமல் தொடர்ந்து மேலோங்குகிறதா?',
        'ஆன்மிக வளர்ச்சி எனக்கு மிகவும் தூரமானது என்று எண்ணுகிறேனா?'
      ]}
    ]
  },
  {
    id:'rajo', title:'ரஜோ',
    topHtml:`<div class="small1"><p>
    <strong>🌺 ரஜோ குணத்தை தெளிவாகப் புரிந்துகொள்வோம் 🌺</strong>
</p>
<p>
    ரஜோ குணம் என்பது செயல், ஆசை, அவசரம், போட்டி, அலைச்சல் ஆகியவற்றை உருவாக்கும் உள் சக்தி.<br>
    இது நம் வாழ்க்கையின் இயக்க சக்தி என்று சொல்லலாம்.<br>
    சரியான அளவில் ரஜஸ் இருந்தால்:
</p>
<p>
    முன்னேற்றம் ஏற்படும்,
</p>
<p>
    வாழ்க்கை வளர்ச்சி அடையும்,
</p>
<p>
    உற்சாகத்துடன் புதிய முயற்சிகள் மேற்கொள்ள முடியும்.
</p>
<p>
    ஆனால் ரஜஸ் அதிகமாக இருந்தால் அது தீய தீயாக மாறி:
</p>
<p>
    மனஅழுத்தம்,
</p>
<p>
    பேராசை,
</p>
<p>
    உறவுகளில் குழப்பம்,
</p>
<p>
    ஆன்மிக சோர்வு<br>
    போன்ற பிரச்சினைகளை ஏற்படுத்தும்.
</p>
<p>
    தேவி பாகவதம் கூறும் படி, லக்ஷ்மி சக்தி தான் ரஜஸை சமப்படுத்தி சரியான திசையில் மாற்றும் தெய்வீக சக்தி.<br>
    ஆனால் முதலில் நம்முள் ரஜஸ் எவ்வளவு உள்ளது என்பதைத் தெளிவாக அறிந்து கொள்ள வேண்டும்.<br>
    அதற்காகவே இந்த சுய பரீட்சை வடிவமைக்கப்பட்டுள்ளது.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>ரஜஸ் எவ்வாறு நம்முள் செயற்படுகிறது</strong>
</p>
<p>
    ரஜஸ் என்பது காற்றைப் போன்றது.<br>
    அது நம்மை இயக்கமும் மாற்றமும் செய்ய வைக்கும் சக்தி.
</p>
<p>
    அது சமநிலையில் இருந்தால் → நம்மைச் செயல்பட வைக்கும், வளர்ச்சி தரும்.
</p>
<p>
    அது அதிகரித்தால் → நம்மை அடக்கமுடியாத ஆசை, பேராசை, அவசரமாகி விடும்.
</p>
<p>
    அது குறைந்தால் → சோம்பல், மந்தநிலை, முன்னேற்றமின்மை ஏற்படும்.
</p>
<p>
    அதிக ரஜஸ் நம்மை எப்போதும் ஓடிக்கொண்டே இருப்பது போல உணர வைக்கும்.<br>
    மனம் அமைதியை இழக்கும்.<br>
    உடல் எப்போதும் பதட்டத்துடன் இருக்கும்.<br>
    உறவுகள் சண்டைகள், போட்டி மனப்பான்மையால் பாதிக்கப்படும்.<br>
    ஆன்மிகத்தில் கவனம் சிதறி விடும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>இந்த பரீட்சையின் நோக்கம்</strong>
</p>
<p>
    ரஜஸ் எந்த பகுதிகளில் அதிகமாக உள்ளது என்பதை அறிந்து கொள்ளும் வகையில்<br>
    இந்த கேள்விகள் ஐந்து பிரிவாக அமைக்கப்பட்டுள்ளன.<br>
    இப்போது ஒவ்வொரு பிரிவும் என்ன குறிக்கிறது, அதில் உள்ள கேள்விகளின் பயன் என்ன என்பதை பார்ப்போம்:
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>A. ஆசை மற்றும் பேராசை (Desire &amp; Greed)</strong>
</p>
<p>
    ரஜஸ் ஆசையில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    எப்போதும் இன்னும் அதிகம் வேண்டும் என்ற பசி.
</p>
<p>
    பணம், பதவி, புகழ் ஆகியவற்றின் மீதான அதிக பற்றுதல்.
</p>
<p>
    பிறரின் வெற்றியைப் பார்த்து மனம் கலக்கம் அடைதல்.
</p>
<p>
    ஆசைகள் நிறைவேறாத போது கோபம் அல்லது சோகத்தில் மூழ்குதல்.
</p>
<p>
    இந்த பகுதியில் உள்ள கேள்விகள் உங்கள் ஆசை சமநிலையில் உள்ளதா அல்லது பேராசையாக மாறியுள்ளதா என்பதை வெளிப்படுத்தும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த முடிவின் அடிப்படையில், தானம், பகிர்வு மனப்பான்மை, சேவை போன்ற நல்ல பழக்கங்களை வளர்க்க வேண்டிய அவசியத்தை உணரலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>B. அவசரம் மற்றும் அலைச்சல் (Hurry &amp; Restlessness)</strong>
</p>
<p>
    அதிக ரஜஸின் அறிகுறிகள்:
</p>
<p>
    எப்போதும் அவசரமாக செயல்படுவது.
</p>
<p>
    சின்ன விஷயங்களுக்கே அதிக கவலை, மன அழுத்தம்.
</p>
<p>
    உடல் எப்போதும் பதட்டத்துடன் இருக்கிறது.
</p>
<p>
    சுமூகமாக சாப்பிட முடியாமை, வேகமாக நடந்து பேசுவது.
</p>
<p>
    சிறிய விஷயங்களுக்கே கோபம் வருவது.
</p>
<p>
    இந்த பகுதியில் உள்ள கேள்விகள் உங்களின் மன அமைதியையும் சீரான செயல்பாடுகளையும் அளவிடும்.<br>
    உங்கள் வாழ்க்கையில் எவ்வளவு சமநிலை குறைந்து விட்டது என்பதை காண உதவும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த அறிகுறிகள் அதிகமாக இருந்தால்,<br>
    மூச்சுப் பயிற்சி, தியானம், நேர மேலாண்மை போன்றவை அவசியம் என்பதை உணரலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>C. போட்டி மற்றும் ஒப்பீடு (Competition &amp; Comparison)</strong>
</p>
<p>
    போட்டியில் வெளிப்படும் ரஜஸின் பாதிப்பு:
</p>
<p>
    எப்போதும் பிறரை விட முன்னேற வேண்டும் என்ற எண்ணம்.
</p>
<p>
    யாராவது சிறப்பாகச் செய்தால் மனம் துயரம் அடைதல்.
</p>
<p>
    வெற்றிக்காக தவறான வழிகளையும் பரிசீலித்தல்.
</p>
<p>
    பிறர் தோல்வியடைந்தால் உள்ளுக்குள் மகிழ்ச்சி அடைதல்.
</p>
<p>
    தன்னை நிரூபிக்க தொடர்ந்து முயற்சி செய்தல்.
</p>
<p>
    இந்த பகுதியில் உள்ள கேள்விகள் உங்கள் வெற்றியின்மீது உள்ள ஆர்வம்<br>
    உங்களை வளர்ச்சிக்காக தள்ளுகிறதா அல்லது அழிவுக்கான போட்டியாய் மாறுகிறதா என்பதை வெளிப்படுத்தும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    போட்டியை நேர்மறை நோக்கில் மாற்றி, பிறரின் வெற்றியையும் வாழ்த்தும் மனப்பான்மை வளர்க்க வேண்டும் என்பதைக் கற்பிக்கிறது.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>D. உறவுகள் மற்றும் சமூக பழக்கம் (Relationships &amp; Social Behavior)</strong>
</p>
<p>
    உறவுகளில் அதிக ரஜஸின் அறிகுறிகள்:
</p>
<p>
    பிறரின் வாழ்க்கையில் மிகுந்த தலையீடு.
</p>
<p>
    எல்லாம் "என் சொல்படி தான் நடக்க வேண்டும்" என்ற கட்டுப்பாடு.
</p>
<p>
    சின்ன விஷயங்களுக்கே சண்டை, வாக்குவாதம்.
</p>
<p>
    பிறரை நம்பாமல் எல்லாவற்றையும் தானே செய்யும் பழக்கம்.
</p>
<p>
    பிறரின் கருத்துக்களை ஏற்காமல் பிடிவாதம் பிடித்தல்.
</p>
<p>
    இந்த பகுதியில் உள்ள கேள்விகள் உங்கள் உறவுகளில் ரஜஸ் எவ்வளவு குழப்பத்தை ஏற்படுத்துகிறது என்பதை தெளிவுபடுத்தும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    உறவு பிரச்சினைகள் அதிகமாக இருந்தால், கேட்கும் பழக்கம், பொறுமை, அன்பு ஆகியவற்றை வளர்க்கும் அவசியத்தை உணரலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>E. ஆன்மிக சாதனையில் தாக்கம் (Spiritual Practice)</strong>
</p>
<p>
    ஆன்மிகத்தில் அதிக ரஜஸின் பாதிப்பு:
</p>
<p>
    ஜபம், தியானத்தில் கவனம் சிதறுதல்.
</p>
<p>
    ஆன்மிகத்திற்கான நேரம் ஒதுக்காமல், வேலை மற்றும் பொழுதுபோக்கில் நேரத்தை வீணடித்தல்.
</p>
<p>
    சாதனையில் கூட விரைவில் பலன் வேண்டும் என்ற அவசரம்.
</p>
<p>
    கோபம், பொறாமை, ஆணவம் குறையாமல் மேலோங்குதல்.
</p>
<p>
    உலக சுகமே வாழ்க்கையின் முக்கிய இலக்கு என நினைத்தல்.
</p>
<p>
    இந்த பகுதியில் உள்ள கேள்விகள் ஆன்மிகத்தில் உங்களின் மனநிலை எவ்வளவு சிதறியுள்ளது என்பதை வெளிப்படுத்தும்.
</p>
<p>
    <strong>பயன்:</strong><br>
    ரஜஸை சமப்படுத்தும் தியானம், மந்திர ஜபம் போன்றவற்றைத் தொடங்க வேண்டிய அவசியத்தை உணரலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>ரஜஸ் சமநிலை இல்லாமல் இருந்தால் விளைவுகள்</strong>
</p>
<p>
    உடல்: எப்போதும் பதட்டம், சோர்வு, மன அழுத்தம்.
</p>
<p>
    மனம்: கவனம் சிதறல், பயம், அலைச்சல்.
</p>
<p>
    உறவுகள்: சண்டைகள், குழப்பம், தவறான போட்டி.
</p>
<p>
    ஆன்மிகம்: கவனம் இல்லாமை, உண்மையான வளர்ச்சி தடை.
</p>
<p>
    ரஜஸ் அதிகமெனில் நம்முள் நிறைய இயக்கம் இருக்கும்.<br>
    ஆனால் அந்த இயக்கம் நேர்மறை திசையில் செல்லாவிட்டால் அது அழிவுக்கே வழிவகுக்கும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>ஏன் இந்த பரீட்சை அவசியம்?</strong>
</p>
<p>
    இந்த சுய பரீட்சை உங்களுக்கு:
</p>
<p>
    ரஜஸ் எந்த பகுதியில் அதிகமாக உள்ளது என்பதைத் தெளிவாகக் காட்டும்.
</p>
<p>
    எந்த பழக்கங்களை உடனே திருத்த வேண்டும் என்பதை வெளிப்படுத்தும்.
</p>
<p>
    லக்ஷ்மி சக்தி சாதனையை எந்த முறையில் தொடங்க வேண்டும் என்பதற்கான தெளிவை வழங்கும்.
</p>
<p>
    “செயல், ஆசை, அவசரம் — இவை எல்லாம் சமநிலையில் இருந்தால் வளர்ச்சி;<br>
    சமநிலை இல்லாவிட்டால் அதுவே வீழ்ச்சி.” 🌺
</p>
	</div><h2>🌺 ரஜோ குணத்தின் தாக்கத்தை அறிய சுய பரீட்சை 🌺</h2>`,
    bottomHtml:`<div class="small">அதிக மதிப்பெண் பெற்ற பகுதியை கவனியுங்கள்:
<br>
ஆசை, அவசரம், போட்டி, உறவுகள், ஆன்மிகம் ஆகிய பிரிவுகளில் எது அதிக மதிப்பெண் பெற்றது என்பதை பாருங்கள்.<br>

அதுவே உங்கள் ரஜஸ் குணம் மிகுந்து செயல்படும் முக்கிய பகுதி.<br>

குறிப்பிட்ட கேள்விகள்:<br>

"ஆம்" என பதிலளித்த கேள்விகளை தனியாக குறித்துக்கொள்ளுங்கள்.<br>

அவை தான் உங்கள் வாழ்க்கையில் மாற்றம் செய்ய வேண்டிய முக்கிய புள்ளிகள்.<br>

நடவடிக்கை திட்டம்:<br>

ஆசை அதிகமாக இருந்தால் → தானம், சேவை செய்யும் பழக்கம் வளர்த்துக்கொள்ளுங்கள்.<br>

அவசரம் அதிகமாக இருந்தால் → மூச்சுப் பயிற்சி (சம வ்ருத்தி) மற்றும் நேர மேலாண்மை பழக்கம் வளர்த்துக்கொள்ளுங்கள்.<br>

போட்டி அதிகமாக இருந்தால் → பிறரின் வெற்றியை வாழ்த்தும் மனப்பான்மை வளர்த்துக் கொள்ளுங்கள்.<br>

மந்திர சாதனை:<br>

oṁ śrīṁ mahalakṣmyai namaḥ மந்திரத்தை 108 முறை ஜபியுங்கள்.<br>

ஜபத்திற்கு முன் 3 நிமிடம் மெதுவாக ஆழ்ந்த மூச்சு விடுங்கள்.<br>
<br>
<b>வாராந்திர சுய மதிப்பாய்வு</b><br>

7 நாட்கள் கழித்து மீண்டும் இந்த பரீட்சையைச் செய்யுங்கள்.<br>

மதிப்பெண் குறைந்து வருமானால், லக்ஷ்மி சக்தி சமநிலையை ஏற்படுத்தத் தொடங்கியுள்ளது என்று அர்த்தம்.<br>

4 வாரங்கள் தொடர்ந்து இதை செய்து உங்கள் மன வளர்ச்சியை கண்காணியுங்கள்.<br>

சுருக்கமாக<br>

ரஜஸ் குணம் வாழ்க்கையின் இயக்க சக்தி. ஆனால் அது அதிகரித்தால் வாழ்க்கை குழப்பமாகி விடும்.<br>

இந்த சுய பரீட்சை, உங்களுள் ரஜஸ் எவ்வளவு மேலோங்குகிறது என்பதை தெளிவாகக் காட்டும்.
<br>
மதிப்பெண்களின் அடிப்படையில், சாதனை, தியானம், மந்திர ஜபம், நல்ல பழக்கங்கள் ஆகியவற்றின் மூலம் ரஜஸை சமப்படுத்தலாம்.
<br>
<b>செயல், ஆசை, அவசரம் – இவை எல்லாம் சமநிலையில் இருந்தால் வளர்ச்சி;
சமநிலை இல்லாவிட்டால் அதுவே வீழ்ச்சி. 🌺</b></div>`,
    ranges:[{min:0,max:15,txt:'ரஜஸ் தாக்கம் மிகக் குறைவு. மனம் அமைதியாக உள்ளது.'},{min:16,max:30,txt:'ரஜஸ் தாக்கம் நடுத்தர அளவில் உள்ளது. மேம்பாடு தேவை.'},{min:31,max:40,txt:'ரஜஸ் தாக்கம் அதிகம். கட்டுப்பாட்டை வளர்க்க வேண்டும்.'},{min:41,max:50,txt:'ரஜஸ் மிக அதிகமாக உள்ளது. ஆன்மிக/வாழ்க்கை சமநிலை ஆபத்தில் உள்ளது.'}],
    groups:[
      {key:'A',title:'ஆசை மற்றும் பேராசை (Desire & Greed)',solution:'தானம், சேவை செய்யும் பழக்கம் வளர்த்துக்கொள்ளுங்கள்.',q:[
        'இன்று நான் செய்வது அனைத்தும் பொருள், பதவி, புகழ் பெறுவதற்கா?',
        'நான் ஏற்கனவே வைத்திருப்பதை மதிக்காமல் இன்னும் அதிகம் பெற வேண்டும் என்று நினைக்கிறேனா?',
        'பிறர் பெற்ற வெற்றியைப் பார்த்து மனம் கலங்குகிறதா?',
        'என் வாழ்க்கைத் திட்டங்கள் "எப்படி அதிக பணம் சம்பாதிப்பது" என்பதில் மையமா?',
        'ஆசைகள் நிறைவேறாமல் போனால் கோபமா அல்லது சோகமா ஆகிறேனா?'
      ]},
      {key:'B',title:'அவசரம் மற்றும் அலைச்சல் (Hurry & Restlessness)',solution:'மூச்சுப் பயிற்சி (சம வ்ருத்தி) மற்றும் நேர மேலாண்மை பழக்கம் வளர்த்துக்கொள்ளுங்கள்.',q:[
        'வேலை செய்யும்போது எப்போதும் அவசரமாக இருக்கிறேனா?',
        'நாளில் வேலை நிறைய இருந்தாலும் மனநிம்மதி இல்லதா?',
        'சின்ன விஷயங்களுக்கே அதிகமாக கவலைப்பட்டு மன அழுத்தம் உணர்கிறேனா?',
        'சுமூகமாக சாப்பிடுதல், நடந்து செல்வதில் வேகமாக செய்வதா?',
        'சிறிய விஷயங்களில்கூட பிறருக்கு விரைவாக கோபப்படுகிறேனா?'
      ]},
      {key:'C',title:'போட்டி மற்றும் ஒப்பீடு (Competition)',solution:'பிறரின் வெற்றியை வாழ்த்தும் மனப்பான்மை வளர்த்துக் கொள்ளுங்கள்.',q:[
        'பிறரை விட முன்னேறவேண்டும் என்ற எண்ணம் எப்போதும் இருக்கிறதா?',
        'யாரும் என்னை விட சிறப்பாக செயல்படும்போது மனம் துயரமாகிறதா?',
        'பிறரை விட முன்னேறுவதற்காக சில நேரங்களில் தவறான வழிகளை பயன்படுத்துகிறேனா?',
        'என் மகிழ்ச்சி பிறர் தோல்வியடையும்போது அதிகமா?',
        'என் வெற்றியை பிறரிடம் நிரூபிக்க விரும்புகிறேனா?'
      ]},
      {key:'D',title:'உறவுகள் மற்றும் சமூக பழக்கம் (Relationships)',solution:'வினோதமான கட்டுப்பாடுகளை தளர்த்த பயிற்சி; மற்றவர்களை கேட்டறிந்து நடக்கவும்.',q:[
        'பிறரின் வாழ்க்கையில் நான் அதிகமாக தலையிடுகிறேனா?',
        '"என் சொல்படி தான் நடக்க வேண்டும்" என்ற எண்ணம் இருக்கிறதா?',
        'உறவுகளில் சின்ன விஷயங்களுக்கு சண்டை நிகழ்கிறதா?',
        'பிறரை நம்பாமல், எல்லாவற்றையும் நான் மட்டும் செய்ய நினைக்கிறேனா?',
        'பிறரின் ஆலோசனைகளை ஏற்றுக்கொள்ளாமல் பிடிவாதப்படுகிறேனா?'
      ]},
      {key:'E',title:'ஆன்மிக சாதனையில் தாக்கம் (Spiritual Practice)',solution:'தினசரி சிறு நேரம் தியானத்திற்காக ஒதுக்கவும்; அதற்காக திட்டமிடுங்கள்.',q:[
        'ஜபம்/தியானம் செய்யும்போது கவனம் சிதறுகிறதா?',
        'ஆன்மிக சாதனைக்கு நேரம் ஒதுக்காமல் பொழுதுபோக்கு, வேலை ஆகியவற்றில் நேரம் வீணடிக்கிறேனா?',
        'ஆன்மிகம் செய்யும்போது "எப்படி விரைவில் பலன் வரும்" என்று விரும்புகிறேனா?',
        'சாத்தான எண்ணங்கள் குறையாமல் மேலோங்குகிறதா?',
        'தெய்வ நம்பிக்கை இருந்தாலும் வாழ்க்கையின் முக்கிய இலக்கு உலக சுகமா என்று நினைக்கிறேனா?'
      ]}
    ]
  },
  {
    id:'sattva', title:'சத்த்வ',
    topHtml:`<div class="small1"><p>
    <strong>🌼 சத்துவ குணத்தை தெளிவாகப் புரிந்துகொள்வோம் 🌼</strong>
</p>
<p>
    சத்துவ குணம் என்பது நம் உள்ளார்ந்த அமைதி, தெளிவு, கருணை, ஞானம், சமநிலை ஆகியவற்றை உருவாக்கும் தெய்வீக குணமாகும்.<br>
    தேவி பாகவதம் கூறுவது போல, சரஸ்வதி சக்தி தான் சத்துவ குணமாக வெளிப்பட்டு,<br>
    ஜீவனுக்கு ஞானமும் ஆன்மிக உயர்வும் அளிக்கின்றாள்.
</p>
<p>
    சத்துவம் அதிகமாக இருந்தால்:
</p>
<p>
    மனம் எப்போதும் அமைதியாக இருக்கும்.
</p>
<p>
    வாழ்க்கையில் தெளிவு மற்றும் திசை தெரியும்.
</p>
<p>
    கருணை, அன்பு, நல்லுணர்வுகள் அதிகரிக்கும்.
</p>
<p>
    ஆன்மிகத்தில் இயல்பான முன்னேற்றம் கிடைக்கும்.
</p>
<p>
    ஆனால் சத்துவம் குறைந்தால்:
</p>
<p>
    குழப்பம், துன்பம், சண்டை, பேராசை போன்றவை அதிகரிக்கும்.
</p>
<p>
    ரஜஸ் மற்றும் தமஸ் மேலோங்கி வாழ்க்கையை குழப்பமாக்கும்.
</p>
<p>
    சத்துவ குணம் என்பது ஒளி போன்றது.<br>
    அது அதிகரித்தால் நம் மனமும் வாழ்க்கையும் வெளிச்சமாகி முன்னேற்றம் காணும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>சத்துவம் எவ்வாறு நம்முள் செயற்படுகிறது</strong>
</p>
<p>
    சத்துவம் நம்முள் இருக்கும் உள் விளக்கு.<br>
    இந்த விளக்கு பிரகாசமாக இருந்தால்:
</p>
<p>
    சரியான முடிவுகளை எடுக்க முடியும்.
</p>
<p>
    ஆசைகள் மற்றும் கோபத்தை கட்டுப்படுத்தலாம்.
</p>
<p>
    பிறரை அன்புடன் அணுகலாம்.
</p>
<p>
    ஆன்மிகத்தில் ஆழ்ந்த கவனம் பெறலாம்.
</p>
<p>
    ஆனால் இந்த விளக்கு மங்கியிருந்தால்:
</p>
<p>
    மனம் குழப்பமடையும்.
</p>
<p>
    ரஜஸ் (அலைச்சல், ஆசை) மற்றும் தமஸ் (சோம்பல், அறியாமை) மேலோங்கும்.
</p>
<p>
    நம் உள்ளார்ந்த வளர்ச்சி தடைப்படும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>இந்த சுய பரீட்சையின் நோக்கம்</strong>
</p>
<p>
    சத்துவ குணம் உங்கள் வாழ்க்கையில் எந்த அளவுக்கு இயங்குகிறது என்பதை அறிய,<br>
    இந்த பரீட்சை ஐந்து பிரிவாக பிரிக்கப்பட்டுள்ளது.<br>
    ஒவ்வொரு பகுதியும் உங்கள் மனநிலையின் முக்கிய தளங்களை அளவிடும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>A. மன அமைதி மற்றும் தெளிவு (Mental Peace &amp; Clarity)</strong>
</p>
<p>
    சத்துவம் மனத்தில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    சிக்கலான சூழ்நிலைகளிலும் அமைதியாக யோசித்து முடிவெடுக்கும் திறன்.
</p>
<p>
    பிரச்சனைகளை கோபமின்றி, பயமின்றி அணுகும் மனநிலை.
</p>
<p>
    முக்கியமான விஷயங்களில் தெளிவான சிந்தனை.
</p>
<p>
    மனம் எளிதில் சிதறாமல் கவனம் நிலைநிறுத்தல்.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த பகுதி உங்கள் மன அமைதி மற்றும் தெளிவு எவ்வளவு உள்ளது என்பதை வெளிப்படுத்தும்.<br>
    தியானம் மற்றும் மூச்சுப் பயிற்சி மூலம் இந்த பகுதியை மேம்படுத்தலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>B. கருணை மற்றும் நற்குணம் (Compassion &amp; Virtues)</strong>
</p>
<p>
    சத்துவம் கருணையில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    பிறரின் துன்பத்தைப் பார்த்து தானாக உதவ வேண்டும் என்ற உணர்வு.
</p>
<p>
    மன்னிப்பு மனப்பான்மை.
</p>
<p>
    அனைவரையும் சமமாக மதிக்கும் பழக்கம்.
</p>
<p>
    வெற்றியைப் பகிர்ந்து மகிழ்வது.
</p>
<p>
    பொய், வஞ்சகம், துரோகம் ஆகியவற்றிலிருந்து விலகும் உறுதி.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த பகுதி உங்கள் அன்பு, கருணை, நல்லுணர்வு எவ்வளவு உள்ளன என்பதை காட்டும்.<br>
    சேவை மற்றும் தானம் மூலம் கருணையை வளர்க்கலாம்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>C. சுய கட்டுப்பாடு மற்றும் சமநிலை (Self-control &amp; Balance)</strong>
</p>
<p>
    சத்துவம் கட்டுப்பாட்டில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    ஆசைகள், கோபம், பொறாமையை கட்டுப்படுத்தும் திறன்.
</p>
<p>
    கடினமான சூழ்நிலைகளிலும் பொறுமையை காக்கும் மனப்பாங்கு.
</p>
<p>
    நேரத்தை ஒழுங்குபடுத்தி வீணாக்காமல் பயன்படுத்துதல்.
</p>
<p>
    உணவு, தூக்கம், பேச்சில் மிதமாக நடந்து கொள்வது.
</p>
<p>
    விமர்சனத்தை அமைதியாக ஏற்று அதிலிருந்து கற்றுக்கொள்ளுதல்.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த பகுதி உங்களின் ஒழுக்கம் மற்றும் சமநிலை எவ்வளவு உள்ளது என்பதை அளவிடும்.<br>
    யோகா, தியானம் போன்றவை சுய கட்டுப்பாட்டை வலுப்படுத்தும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>D. அறிவு மற்றும் ஆன்மிக ஆர்வம் (Knowledge &amp; Spiritual Inclination)</strong>
</p>
<p>
    சத்துவம் அறிவில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    ஆன்மிக நூல்கள் படிக்க ஆர்வம்.
</p>
<p>
    வாழ்க்கையின் உண்மையான நோக்கம் குறித்து சிந்திக்கும் பழக்கம்.
</p>
<p>
    தினசரி ஜபம் அல்லது தியானத்தில் தொடர்ந்து ஈடுபடுதல்.
</p>
<p>
    உலக வாழ்க்கையும் ஆன்மிகமும் சமநிலையுடன் நடத்துதல்.
</p>
<p>
    செயல்கள் தர்மத்துடன் பொருந்துகிறதா என்பதை ஆராய்தல்.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த பகுதி உங்களின் ஆன்மிக வளர்ச்சி மற்றும் ஞானம் எவ்வளவு உள்ளது என்பதை காட்டும்.<br>
    ஜபம், தியானம், ஆன்மிக வாசிப்பு ஆகியவை இந்த பகுதியை மேம்படுத்தும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>E. உறவுகள் மற்றும் சமூக நற்பணி (Relationships &amp; Service)</strong>
</p>
<p>
    சத்துவம் உறவுகளில் வெளிப்படும் அறிகுறிகள்:
</p>
<p>
    குடும்பத்தினரும் நண்பர்களும் அன்பும் புரிதலும் கொண்டு நடந்து கொள்வது.
</p>
<p>
    சின்ன விஷயங்களுக்காக சண்டைகள், வாக்குவாதங்கள் தவிர்த்தல்.
</p>
<p>
    சமூக நலனுக்காக பங்களிப்பு செய்தல்.
</p>
<p>
    பிறரின் வெற்றியை மனமார வாழ்த்துதல்.
</p>
<p>
    "நான்" என்ற சுயநலத்தை குறைத்து "நாம்" என்ற எண்ணத்தை வளர்த்தல்.
</p>
<p>
    <strong>பயன்:</strong><br>
    இந்த பகுதி உங்கள் உறவுகள் மற்றும் சமூக பங்களிப்பு எவ்வளவு நேர்மறையாக உள்ளது என்பதை வெளிப்படுத்தும்.<br>
    சேவை மற்றும் சமூக பணி இந்த பகுதியை வலுப்படுத்தும்.
</p>
<p>
    &nbsp;
</p>
<p>
    <strong>சத்துவம் அதிகமெனில் கிடைக்கும் நன்மைகள்</strong>
</p>
<p>
    மனம்: அமைதி, தெளிவு, ஒருமுக சிந்தனை.
</p>
<p>
    உடல்: ஆரோக்கியம், உற்சாகம், சீரான ஆற்றல்.
</p>
<p>
    உறவுகள்: அன்பு, கருணை, சமநிலை.
</p>
<p>
    ஆன்மிகம்: ஞானம், உயர்ந்த அனுபவம், பரிசுத்த நிலை.
</p>
<p>
    &nbsp;
</p>
<p>
    “சத்துவம் வளர்ந்தால் மனம் ஒளியாகும்;<br>
    ஒளியான மனம் ஆன்மிகப் பாதையை வெளிச்சப்படுத்தும்.” 🌼
</p>
<p>
    சத்துவத்தை வளர்த்து, உங்கள் மனத்தையும் வாழ்க்கையையும் வெளிச்சமாக மாற்றிக் கொள்ளுங்கள்.<br>
    இதுவே ஆன்மிக முன்னேற்றத்தின் நிச்சயமான அடிப்படை. 🌟
</p></div><h2>🌼 சத்த்வ குணத்தின் தாக்கத்தை அறிய சுய பரீட்சை 🌼
</h2>`,
    bottomHtml:`<div class="small">அதிக மதிப்பெண் பெற்ற பகுதியை கவனியுங்கள்:
<br>
5 பிரிவுகளில் (மனம், கருணை, கட்டுப்பாடு, ஆன்மிகம், உறவுகள்) எந்த பிரிவு அதிக மதிப்பெண் பெற்றது என்பதை பாருங்கள்.<br>

அதுவே உங்கள் சத்த்வம் மிகச் சிறப்பாக இயங்கும் பகுதி.
<br>
குறைந்த மதிப்பெண் பெற்ற பிரிவே உங்கள் சத்த்வத்தை வளர்க்க வேண்டிய முக்கிய திசை.
<br>
உதாரணம்:
<br>
"மனம் மற்றும் தெளிவு" பகுதியில் குறைந்த மதிப்பெண் → தியானம் மற்றும் மூச்சுப் பயிற்சி செய்யவும்.
<br>
"உறவுகள்" பகுதியில் குறைந்த மதிப்பெண் → பொறுமையும் அன்பும் வளர்க்கவும்.
<br>

குறிப்பிட்ட கேள்விகள்:<br>

"இல்லை" என்று பதிலளித்த கேள்விகளை தனியாக குறித்துக் கொள்ளுங்கள்.
<br>

அவையே உங்கள் வாழ்க்கையில் உடனடியாக மாற்றம் செய்ய வேண்டிய முக்கிய இடங்கள்.
<br>

சத்த்வத்தை வளர்க்கும் நடைமுறை:<br>
தினசரி தியானம்: 10–15 நிமிடம் அமைதியாக அமர்ந்து மூச்சு கவனிப்பு செய்யுங்கள்.<br>

மந்திர ஜபம்: oṁ aiṁ sarasvatyai namaḥ – 108 முறை.<br>

நன்றி பழக்கம்: தினமும் இரவு, இன்று நடந்த 3 நல்ல விஷயங்களுக்கு நன்றி கூறுங்கள்.<br>

தானம் மற்றும் சேவை: வாரத்திற்கு குறைந்தது ஒரு செயல் பிறருக்காக செய்யுங்கள்.<br>

சத்தியம் மற்றும் மிதசாரம்: பேச்சிலும் செயலிலும் சத்தியம், உணவில் மிதசாரம் கடைபிடிக்கவும்.

<br>
<b>வாராந்திர சுய மதிப்பாய்வு</b><br>
7 நாட்கள் கழித்து மீண்டும் சுய பரீட்சையைச் செய்யுங்கள்.<br>

மதிப்பெண் உயர்ந்து வந்தால், சத்த்வம் வளர்ந்து கொண்டிருக்கிறது.<br>

4 வாரங்களில் மாற்றத்தை தெளிவாகக் காண முடியும்.<br>

சுருக்கம்<br>

சத்த்வ குணம் அதிகமெனில் மன அமைதி, தெளிவு, கருணை, ஆன்மிக உயர்வு கிடைக்கும்.
<br>
இந்த சுய பரீட்சை உங்கள் உள்ளார்ந்த நிலையை வெளிப்படுத்தும் கண்ணாடி போல செயல்படும்.
<br>
மதிப்பெண்களை அடிப்படையாக கொண்டு, தியானம், மந்திர ஜபம், சேவை, நல்ல பழக்கங்கள் மூலம் சத்த்வத்தை மேம்படுத்தலாம்.
<br>


<b>சத்த்வம் வளர்ந்தால் மனம் விளக்காகும்;
விளக்கான மனம் ஆன்மிகப் பாதையை வெளிச்சப்படுத்தும். 🌼</b></div>`,
    ranges:[{min:0,max:15,txt:'சத்த்வம் மிகவும் குறைவு. மனம் குழப்பம், ரஜஸ் மற்றும் தமஸ் அதிகம்.'},{min:16,max:30,txt:'சத்த்வம் நடுத்தர அளவில் உள்ளது. சீராக வளர்க்க வேண்டும்.'},{min:31,max:40,txt:'சத்த்வம் நல்ல நிலையில் உள்ளது. மேலும் வளர்ச்சி சாத்தியம்.'},{min:41,max:50,txt:'சத்த்வம் மிக உயர்ந்த நிலை. ஆன்மிக முன்னேற்றத்திற்கு சிறந்த அடிப்படை.'}],
    groups:[
      {key:'A',title:'மன அமைதி மற்றும் தெளிவு (Mental Peace & Clarity)',solution:'தியானம் மற்றும் மூச்சுப் பயிற்சி செய்யவும்.',q:[
        'சிக்கலான சூழ்நிலைகளிலும் நான் அமைதியாக யோசித்து முடிவெடுக்குகிறேனா?',
        'ஒரு பிரச்சனையை பார்க்கும்போது கோபம்/பயம்/குழப்பம் இல்லாமல் அணுகுகிறேனா?',
        'எனக்கு முக்கிய விஷயங்களில் தெளிவான சிந்தனை இருக்கிறதா?',
        'எளிதில் மனம் சிதறாமல் ஒரு செயலில் கவனம் செலுத்துகிறேனா?',
        'பிறரின் பேச்சை குறைசொல்லாமல் அமைதியாகக் கேட்கிறேனா?'
      ]},
      {key:'B',title:'கருணை மற்றும் நற்குணம் (Compassion & Virtues)',solution:'',q:[
        'பிறர் துன்பத்தில் இருப்பதைப் பார்த்தால் உதவுவது தானாக வருகிறதா?',
        'என்னை துன்பப்படுத்தியவர்களையும் மன்னிக்க தெரிகிறதா?',
        'யாரையும் அவமதிக்காமல் சமமாக மதிக்கிறேனா?',
        'என் வெற்றி பிறரையும் மகிழ்ச்சி அடையச் செய்யும் அளவிற்கு பகிர்வா?',
        'பொய்/வஞ்சகம்/துரோகம் போன்றவற்றைத் தவிர்க்குமா?'
      ]},
      {key:'C',title:'சுய கட்டுப்பாடு மற்றும் சமநிலை (Self-control & Balance)',solution:'',q:[
        'ஆசைகள்/கோபம்/பொறாமை வந்தாலும் அவற்றைக் கட்டுப்படுத்துகிறேனா?',
        'கடின சூழ்நிலைகளிலும் பொறுமையை இழக்காமா இருக்கிறேனா?',
        'நேரத்தை ஒழுங்குபடுத்தி வீணாக செலவிடாமல் இருக்கிறேனா?',
        'சாப்பாடு/தூக்கம்/பேச்சு ஆகியவற்றில் மிதமாக இருக்கிறீர்களா?',
        'விமர்சனங்களை அமைதியாக ஏற்றுக்கொண்டு கற்றுக்கொள்கிறேனா?'
      ]},
      {key:'D',title:'அறிவு மற்றும் ஆன்மிக ஆர்வம் (Knowledge & Spiritual Inclination)',solution:'',q:[
        'ஆன்மிக நூல்கள் படிப்பதில் ஆர்வம் உண்டா?',
        'வாழ்க்கையின் உண்மையான நோக்கம் பற்றி சிந்திப்பிருக்கிறீர்களா?',
        'தினசரி ஜபம்/தியானம் செய்வதற்கான பழக்கம் தொடர்ச்சியாக இருக்கிறதா?',
        'ஆன்மிகம் மற்றும் உலக வாழ்க்கையை சமநிலையுடன் நடத்த முயற்சி செய்கிறீர்களா?',
        'என் செயல்கள் தர்மத்துடன் பொருந்துகிறதா என்று ஆராயுகிறீர்களா?'
      ]},
      {key:'E',title:'உறவுகள் மற்றும் சமூக நற்பணி (Relationships & Service)',solution:'பொறுமையும் அன்பும் வளர்க்கவும்.',q:[
        'குடும்பத்தினருக்கும் நண்பர்களுக்கும் அன்பும் புரிதலும் கொண்டிருக்கிறீர்களா?',
        'சின்ன விஷயங்களுக்கு சண்டை/வாக்குவாதம் இல்லாமல் இருக்கிறீர்களா?',
        'சமூக நலனுக்காக பங்களிக்கிறீர்களா?',
        'பிறரின் வெற்றியை மனமார வாழ்த்துகிறீர்களா?',
        '"நான் மட்டுமே" என்ற சுயச்சர்ம குறைந்து "நாம்" என்ற எண்ணம் வளர்ந்திருக்கிறதா?'
      ]}
    ]
  }
];

// App state
const state = {
  currentTabIndex:0,
  answers: {}, // answers[tabId][groupKey][qIndex] = 0/1/2
  done: {} // done[tabId]=true when calculated
};

const tabsEl = document.getElementById('tabs');
const panel = document.getElementById('panel');
const finalSummary = document.getElementById('finalSummary');

function init(){
  TABS.forEach((t,i)=>{
    const b = document.createElement('button'); b.className='tab-btn'+(i===0?' active':''); b.textContent=t.title; b.onclick=()=>switchTab(i);
    //b.disabled = i!==0; // only first tab enabled initially
    tabsEl.appendChild(b);
  });
  renderTab(0);
}

function switchTab(i){
  state.currentTabIndex = i;
  Array.from(tabsEl.children).forEach((b,idx)=>{b.classList.toggle('active', idx===i)});
  renderTab(i);
}

function renderTab(i){
  const t = TABS[i];
  panel.innerHTML = '';
  const top = document.createElement('div'); top.className='top-text'; top.innerHTML = t.topHtml; panel.appendChild(top);

  // build accordion-like groups
  t.groups.forEach(g=>{
    const details = document.createElement('details'); details.className='group';
    const summary = document.createElement('summary'); summary.textContent = g.key + '. ' + g.title;
    details.appendChild(summary);
    const qdiv = document.createElement('div'); qdiv.className='questions';
    g.q.forEach((qq,qi)=>{
      const qbox = document.createElement('div'); qbox.className='q';
      const lab = document.createElement('label'); lab.textContent = (qi+1)+'. ' + qq;
      const ans = document.createElement('div'); ans.className='answers';
      ['ஆம்','சில நேரங்களில்','இல்லை'].forEach((opt,oi)=>{
        const id = `r_${t.id}_${g.key}_${qi}_${oi}`;
        const rlab = document.createElement('label');
        const inp = document.createElement('input'); inp.type='radio'; inp.name=`ans_${t.id}_${g.key}_${qi}`; inp.value = oi===0?2: (oi===1?1:0); inp.id=id;
        rlab.appendChild(inp); rlab.appendChild(document.createTextNode(' '+opt));
        ans.appendChild(rlab);
      });
      qbox.appendChild(lab); qbox.appendChild(ans);
      qdiv.appendChild(qbox);
    });
    details.appendChild(qdiv);
    panel.appendChild(details);
  });

  // controls
  const ctrl = document.createElement('div'); ctrl.className='controls';
  const calc = document.createElement('button'); calc.className='calc'; calc.textContent='மதிப்பெண்'; calc.onclick = ()=>calculateCurrentTab();
  ctrl.appendChild(calc);
  const next = document.createElement('button'); next.className='calc next'; next.textContent='அடுத்த குணம்'; next.onclick = ()=>gotoNext(); next.disabled = true;
  ctrl.appendChild(next);
  panel.appendChild(ctrl);


  const result = document.createElement('div'); result.className='result hidden'; result.id='result_'+t.id; panel.appendChild(result);
  
  
  const bottom = document.createElement('div'); bottom.className='small'; bottom.innerHTML = t.bottomHtml; panel.appendChild(bottom);

  // If already calculated before, pre-fill results and allow next
  if(state.done[t.id]){ next.disabled = false; showResultForTab(t.id); }
}

function calculateCurrentTab(){
  const t = TABS[state.currentTabIndex];
  const tabId = t.id;
  const groupTotals = {};
  let tabTotal = 0;
  let unanswered=false;
  groupTotalsArray = [];
  t.groups.forEach(g=>{
    let gsum = 0; g.q.forEach((qq,qi)=>{
      const name = `ans_${tabId}_${g.key}_${qi}`;
      const sel = document.querySelector(`input[name="${name}"]:checked`);
	  if(!sel) unanswered=true;
      const val = sel? Number(sel.value) : 0; // unanswered treated as 0
      gsum += val;
      // save
      state.answers[tabId] = state.answers[tabId] || {};
      state.answers[tabId][g.key] = state.answers[tabId][g.key] || [];
      state.answers[tabId][g.key][qi] = val;
    });
    groupTotals[g.key]=gsum; tabTotal += gsum;
  });
if(unanswered){
alert('தயவுசெய்து அனைத்து கேள்விகளுக்கும் பதில் அளிக்கவும்.');
return;
}

  state.done[tabId]=true;
  // show result
  showResultForTab(tabId, groupTotals, tabTotal);
  // enable next tab button if exists
  const nextBtn = panel.querySelector('button.next'); if(nextBtn){ nextBtn.disabled = false; }
  // enable next tab in header
  const idx = state.currentTabIndex; if(idx+1 < TABS.length){ tabsEl.children[idx+1].disabled = false; }
}

function showResultForTab(tabId, groupTotalsArg, tabTotalArg){
  const t = TABS.find(x=>x.id===tabId);
  const resultEl = document.getElementById('result_'+tabId);
  // if args not provided, recompute from state
  let groupTotals = groupTotalsArg || {};
  let tabTotal = tabTotalArg || 0;
  if(!groupTotalsArg){
    tabTotal=0; t.groups.forEach(g=>{ const arr = (state.answers[tabId] && state.answers[tabId][g.key])||[]; let s=0; for(let v of arr) s+= (v||0); groupTotals[g.key]=s; tabTotal+=s; });
  }
  // range description
  const range = t.ranges.find(r=> tabTotal>=r.min && tabTotal<=r.max);
  resultEl.innerHTML = ` <strong>மொத்தம்: ${tabTotal} / 50</strong><div class="small">${range?range.txt:''}</div>`;
  // group table
  const table = document.createElement('table');
  const thead = document.createElement('thead'); thead.innerHTML = '<tr><th>பாகம்</th><th>மதிப்பெண்</th></tr>'; table.appendChild(thead);
  const tbody = document.createElement('tbody');
  t.groups.forEach(g=>{
    const tr = document.createElement('tr');
    const td1 = document.createElement('td'); td1.textContent = g.key + '. ' + g.title;
    const td2 = document.createElement('td'); td2.textContent = (groupTotals[g.key]||0) + ' / 10';
    tr.appendChild(td1); tr.appendChild(td2); tbody.appendChild(tr);
  });
  table.appendChild(tbody);
  resultEl.appendChild(table);

  let chosenKey=null,chosenVal=null;
if(tabId==='sattva'){
let minVal=Infinity;
t.groups.forEach(g=>{const v=groupTotals[g.key]||0; if(v<minVal){minVal=v; chosenKey=g.key; chosenVal=v;}});
} else {
let maxVal=-1;
t.groups.forEach(g=>{const v=groupTotals[g.key]||0; if(v>maxVal){maxVal=v; chosenKey=g.key; chosenVal=v;}});
}


const chosenGroup=t.groups.find(g=>g.key===chosenKey);
if(chosenGroup){
const row=tbody.querySelector(`tr[data-key="${chosenGroup.key}"]`);
if(row) row.classList.add('highlight');
if(chosenGroup.solution){
const solDiv=document.createElement('div'); solDiv.style.marginTop='8px';
solDiv.innerHTML=`<strong>${tabId==='sattva'?'குறைந்த':'மேலோங்கிய'} பகுதி:</strong> ${chosenGroup.key} — ${chosenGroup.title} (${chosenVal}/10)`;
solDiv.innerHTML+=`<div class="small" style="margin-top:6px"><strong>பரிந்துரை:</strong> ${chosenGroup.solution}</div>`;
resultEl.appendChild(solDiv);
}
}

  resultEl.classList.remove('hidden');
}

function gotoNext(){
  const idx = state.currentTabIndex; if(idx+1 < TABS.length){ switchTab(idx+1); } else { //showFinalSummary(); 
  }
}

function showFinalSummary(){
  finalSummary.classList.remove('hidden'); finalSummary.innerHTML='';
  const box = document.createElement('div'); box.className='summary';
  box.innerHTML = '<h3>முடிந்த மதிப்பாய்வு — சுருக்கம்</h3>';
  const tbl = document.createElement('table'); tbl.innerHTML = '<tr><th>குணம்</th><th>மொத்தம்</th><th>நிலைக் குறிப்பு</th></tr>';
  let combined = 0;
  TABS.forEach(t=>{
    let total=0; t.groups.forEach(g=>{ const arr = (state.answers[t.id] && state.answers[t.id][g.key])||[]; for(let v of arr) total += (v||0); });
    combined += total;
    const range = t.ranges.find(r=> total>=r.min && total<=r.max);
    const tr = document.createElement('tr'); tr.innerHTML = `<td>${t.title}</td><td>${total} / 50</td><td>${range?range.txt:''}</td>`; tbl.appendChild(tr);
  });
  box.appendChild(tbl);
  const comb = document.createElement('div'); comb.style.marginTop='10px'; comb.innerHTML = `<strong>ஒட்டுமொத்த மதிப்பெண் (மூன்று குணம்):</strong> ${combined} / 150`;
  box.appendChild(comb);

  // show high groups per tab
const adv=document.createElement('div'); adv.style.marginTop='10px'; adv.innerHTML='<strong>ஒவ்வொரு குணத்திற்கும் முக்கிய பகுதி:</strong>';
const ul=document.createElement('ul');
TABS.forEach(t=>{
let chosenKey=null,chosenVal=null;
if(t.id==='sattva'){
let minVal=Infinity;
t.groups.forEach(g=>{const arr=(state.answers[t.id]&&state.answers[t.id][g.key])||[]; let s=0; for(let v of arr) s+=(v||0); if(s<minVal){minVal=s; chosenKey=g.key; chosenVal=s;}});
} else {
let maxVal=-1;
t.groups.forEach(g=>{const arr=(state.answers[t.id]&&state.answers[t.id][g.key])||[]; let s=0; for(let v of arr) s+=(v||0); if(s>maxVal){maxVal=s; chosenKey=g.key; chosenVal=s;}});
}
const g=t.groups.find(x=>x.key===chosenKey);
if(g&&g.solution){
const li=document.createElement('li');
li.innerHTML=`${t.title}: ${g.key} - ${g.title} (${chosenVal}/10) → பரிந்துரை: ${g.solution}`;
ul.appendChild(li);
}
});
  adv.appendChild(ul); box.appendChild(adv);

  finalSummary.appendChild(box);
}

init();
</script>